import React, { useState, useEffect } from "react";
import { supabase } from "../../lib/supabase";
import { useRole } from "../../hooks/useRole";
import { useToast } from "../ui/ToastProvider";
import { getAvailableRoles, getRoleDisplayName } from "../../utils/roles";
import LoadingSpinner from "../ui/LoadingSpinner";
import Icon from "../AppIcon";

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterRole, setFilterRole] = useState("all");
  const { isAdmin } = useRole();
  const { addToast } = useToast();

  useEffect(() => {
    if (isAdmin()) {
      fetchUsers();
    }
  }, [isAdmin]);

  const fetchUsers = async () => {
    try {
      setLoading(true);

      // Use the user_management_view that joins user_profiles with auth.users
      const { data, error } = await supabase
        .from("user_management_view")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      // Transform the data to match the expected structure
      const transformedUsers = (data || []).map((row) => ({
        id: row.id,
        user_id: row.user_id,
        role: row.role,
        first_name: row.first_name,
        last_name: row.last_name,
        created_at: row.created_at,
        updated_at: row.updated_at,
        user: {
          email: row.email,
          created_at: row.auth_created_at,
          last_sign_in_at: row.last_sign_in_at,
        },
      }));

      setUsers(transformedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      addToast("Failed to load users", "error");
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (userId, newRole) => {
    try {
      setUpdating(userId);
      const { error } = await supabase
        .from("user_profiles")
        .update({ role: newRole })
        .eq("user_id", userId);

      if (error) {
        throw error;
      }

      // Update local state
      setUsers(
        users.map((user) =>
          user.user_id === userId ? { ...user, role: newRole } : user
        )
      );

      addToast("User role updated successfully", "success");
    } catch (error) {
      console.error("Error updating user role:", error);
      addToast("Failed to update user role", "error");
    } finally {
      setUpdating(null);
    }
  };

  if (!isAdmin()) {
    return (
      <div className="text-center py-8">
        <p className="text-text-secondary">
          Access denied. Admin privileges required.
        </p>
      </div>
    );
  }

  if (loading) {
    return <LoadingSpinner message="Loading users..." />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-text-primary">
          User Management
        </h2>
        <button
          onClick={fetchUsers}
          className="btn btn-outline flex items-center gap-2"
          disabled={loading}
        >
          <Icon name="RefreshCw" size={16} />
          Refresh
        </button>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label
              htmlFor="search"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Search Users
            </label>
            <div className="relative">
              <Icon
                name="Search"
                size={16}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                id="search"
                type="text"
                placeholder="Search by email or name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <label
              htmlFor="roleFilter"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Filter by Role
            </label>
            <select
              id="roleFilter"
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Roles</option>
              {getAvailableRoles().map((role) => (
                <option key={role.value} value={role.value}>
                  {role.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Joined
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Sign In
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users
                .filter((user) => {
                  const matchesRole =
                    filterRole === "all" || user.role === filterRole;
                  const matchesSearch =
                    searchTerm === "" ||
                    user.user?.email
                      ?.toLowerCase()
                      .includes(searchTerm.toLowerCase()) ||
                    `${user.first_name || ""} ${user.last_name || ""}`
                      .toLowerCase()
                      .includes(searchTerm.toLowerCase());
                  return matchesRole && matchesSearch;
                })
                .map((userProfile) => (
                  <tr key={userProfile.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600">
                              {userProfile.user?.email
                                ?.charAt(0)
                                .toUpperCase() || "U"}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {userProfile.first_name && userProfile.last_name
                              ? `${userProfile.first_name} ${userProfile.last_name}`
                              : userProfile.user?.email || "Unknown User"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {userProfile.user?.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          userProfile.role === "admin"
                            ? "bg-red-100 text-red-800"
                            : userProfile.role === "agent"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        {getRoleDisplayName(userProfile.role)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {userProfile.user?.created_at
                        ? new Date(
                            userProfile.user.created_at
                          ).toLocaleDateString()
                        : "Unknown"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {userProfile.user?.last_sign_in_at
                        ? new Date(
                            userProfile.user.last_sign_in_at
                          ).toLocaleDateString()
                        : "Never"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <select
                        value={userProfile.role}
                        onChange={(e) =>
                          updateUserRole(userProfile.user_id, e.target.value)
                        }
                        disabled={updating === userProfile.user_id}
                        className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      >
                        {getAvailableRoles().map((role) => (
                          <option key={role.value} value={role.value}>
                            {role.label}
                          </option>
                        ))}
                      </select>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </div>

      {users.filter((user) => {
        const matchesRole = filterRole === "all" || user.role === filterRole;
        const matchesSearch =
          searchTerm === "" ||
          user.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          `${user.first_name || ""} ${user.last_name || ""}`
            .toLowerCase()
            .includes(searchTerm.toLowerCase());
        return matchesRole && matchesSearch;
      }).length === 0 && (
        <div className="text-center py-8">
          <p className="text-text-secondary">
            {users.length === 0
              ? "No users found."
              : "No users match the current filters."}
          </p>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
