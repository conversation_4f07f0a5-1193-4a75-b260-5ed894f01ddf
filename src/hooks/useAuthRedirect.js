import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ROLES } from '../utils/roles';

/**
 * Hook to handle authentication-based redirects
 * This should be used in components that need to redirect based on auth state
 */
export const useAuthRedirect = () => {
  const { user, userProfile, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Don't redirect if still loading
    if (loading) return;

    // Don't redirect if no user
    if (!user) return;

    // Don't redirect if no profile yet
    if (!userProfile) return;

    console.log("🔄 Auth redirect check:", { 
      user: user.email, 
      role: userProfile.role,
      currentPath: location.pathname 
    });

    // Admin redirect logic
    if (userProfile.role === ROLES.ADMIN) {
      // Only redirect if not already on admin pages
      if (!location.pathname.startsWith('/admin')) {
        console.log("✅ Redirecting admin to /admin");
        navigate('/admin', { replace: true });
      }
    } else {
      // Regular user redirect logic (only if on login page)
      if (location.pathname === '/login') {
        const from = location.state?.from?.pathname || '/profession-selection-landing';
        console.log("✅ Redirecting user to", from);
        navigate(from, { replace: true });
      }
    }
  }, [user, userProfile, loading, navigate, location]);

  return {
    user,
    userProfile,
    loading,
    isAuthenticated: !!user,
    isAdmin: userProfile?.role === ROLES.ADMIN
  };
};
