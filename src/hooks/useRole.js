import { useAuth } from "../contexts/AuthContext";
import { hasRole, isAdmin, isAgent, isBasicUser, ROL<PERSON> } from "../utils/roles";

/**
 * Custom hook for role-based access control
 * @returns {Object} Role utilities and checks
 */
export const useRole = () => {
  const { user, userProfile, loading } = useAuth();

  const userRole = userProfile?.role || ROLES.USER;

  return {
    // Current user role
    role: userRole,

    // Loading state
    loading,

    // Role checks
    hasRole: (requiredRole) => hasRole(userRole, requiredRole),
    isAdmin: () => isAdmin(userRole),
    isAgent: () => isAgent(userRole),
    isBasicUser: () => isBasicUser(userRole),

    // Specific permission checks
    canAccessAgentFeatures: () => isAgent(userRole),
    canAccessAdminPanel: () => isAdmin(userRole),
    canManageUsers: () => isAdmin(userRole),
    canViewFullReports: () => isAgent(userRole),
    canAccessGapCalculator: () => isAgent(userRole),

    // User info
    isAuthenticated: !!user,
    userId: user?.id,
    userEmail: user?.email,
    userProfile,
  };
};
